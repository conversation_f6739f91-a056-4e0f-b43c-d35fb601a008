---
created: 2024-09-09T16:21:44
updated: 2025-05-14T12:04:47
---

- **Key Collection**: The player needs to find the **key to the brother's room**, which is located on top of the fridge in the kitchen.
- **Morse Code Decoding**: The player needs to **decode Morse code** found in the house, potentially related to the brother's PC password.\
	-Password: arjun1990
- **Password Entry (Brother's PC)**: The player needs to figure out the **password for the brother's locked PC**, which might be the Morse code or something else found within the brother's room.
	-Email: <EMAIL>
	-Password: akshay1985
- **Power Manipulation (MCB Boxes)**: The player must **turn on the lights in one room at a time by interacting with the MCB boxes**. Only one room can have power at any given time.
- **Flashlight Usage**: The player needs to **find and use a flashlight** to navigate the dark house after the power goes out.
- **Environmental Illumination (Lighter)**: The player can use a **Lighter in the courtyard to light up the immediate surroundings**.
- **Fear Meter/Ghost Avoidance**: While the power is out, a **"fear meter" and heartbeat sound indicate the proximity of the ghost**. The player must avoid the ghost and prevent the fear meter from filling up to avoid losing inventory.
- **Number Lock Puzzle (Chest)**: The player needs to **open a three-digit number lock on an old chest** using a provided hint and a three-digit number from Arjun's old school ID card.
- **Decryptor.exe Interaction**: The player attempts to use a **"decryptor.exe" file on the brother's PC** by inputting alphanumeric characters from the suspicious email, although it doesn't yield any useful results.
- **CCTV Monitoring**: The player briefly witnesses the **ghost's appearance through a running CCTV application on the brother's PC**.


