

This tool helps you simulate how your equity percentage will change based on the hours you contribute over time. Enter your simulation parameters below to see how your equity stake will evolve.

## Simulation Parameters

```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// These should match the paths in Equity.md
const referenceFilePath = "99 - data/equitySplit/Equity Reference.md"; // <== CHECK PATH
const foundersFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK NAMES
const founderNames = ["Shashank", "Anshul", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES

// --- Helper Function for Logging ---
function simulatorLogDebug(message, ...args) {
    console.log(`[VestingSimulator] ${message}`, args.length > 0 ? args : "");
}

// --- Main Function ---
(function() {
    simulatorLogDebug("Starting vesting simulator...");

    try {
        // --- Load Contribution Files ---
        simulatorLogDebug(`Processing files from folder: ${foundersFolderPath}`);
        const files = dv.pages(foundersFolderPath);
        simulatorLogDebug(`Found ${files.length} files.`);

        if (files.length === 0) {
            dv.el("p", `ℹ️ No contribution files found.`);
            return;
        }

        // --- Load Reference Data ---
        simulatorLogDebug(`Attempting to load reference file: ${referenceFilePath}`);
        const referencePage = dv.page(referenceFilePath);

        if (!referencePage) {
            dv.el("p", `❌ **Error:** Cannot find the Equity Reference file at '${referenceFilePath}'.`);
            throw new Error("Reference file not found.");
        }
        simulatorLogDebug("Reference file loaded successfully.");

        // --- Initialize Scores, Raw Hours, and Base Values ---
        let scores = {};
        let rawHours = {};
        let baseValues = {};
        let initializationErrors = false;

        founderNames.forEach(name => {
            scores[name] = 0;
            rawHours[name] = 0;

            const baseValueStr = referencePage[name];
            simulatorLogDebug(`Initializing ${name}: Found base value string: '${baseValueStr}'`);
            if (baseValueStr !== undefined && baseValueStr !== null) {
                const baseValue = parseInt(baseValueStr, 10);
                if (!isNaN(baseValue)) {
                    baseValues[name] = baseValue;
                } else {
                    dv.el("p", `⚠️ **Warning:** Invalid base value for '${name}' in reference ('${baseValueStr}'). Contributions ignored.`);
                    initializationErrors = true;
                    delete scores[name]; delete rawHours[name];
                }
            } else {
                dv.el("p", `⚠️ **Warning:** Founder '${name}' not found in reference ('${referenceFilePath}'). Contributions ignored.`);
                initializationErrors = true;
                delete scores[name]; delete rawHours[name];
            }
        });

        if (initializationErrors) { dv.el("p", "🛑 Check reference file warnings."); }
        simulatorLogDebug("Base values parsed:", baseValues);
        simulatorLogDebug("Initial scores:", scores);
        simulatorLogDebug("Initial raw hours:", rawHours);

        // --- Process Contribution Files ---
        files.forEach((item) => {
            try {
                const founderName = item.name;
                if (!founderNames.includes(founderName)) return;
                if (!baseValues.hasOwnProperty(founderName)) return;

                const countValue = item.count;
                if (countValue === undefined || countValue === null) return;

                const numericCount = parseFloat(countValue);
                if (isNaN(numericCount)) return;

                const baseValue = baseValues[founderName];
                const weightedContribution = baseValue * numericCount;

                scores[founderName] += weightedContribution;
                rawHours[founderName] += numericCount;
            } catch (err) {
                simulatorLogDebug(`Error processing file: ${err.message}`);
            }
        });

        // --- Calculate Total Score ---
        let grandTotalScore = Object.values(scores).reduce((sum, score) =>
            (typeof score === 'number' && !isNaN(score) ? sum + score : sum), 0);

        // Calculate current percentages
        const currentPercentages = {};
        founderNames.forEach(name => {
            if (baseValues.hasOwnProperty(name)) {
                currentPercentages[name] = grandTotalScore > 0 ?
                    (scores[name] / grandTotalScore) * 100 : 0;
            } else {
                currentPercentages[name] = 0;
            }
        });

        // --- Create UI Container ---
        const simulatorContainer = dv.el("div", "", {cls: "simulator-container"});
        simulatorContainer.style.backgroundColor = "#ffffff";
        simulatorContainer.style.borderRadius = "10px";
        simulatorContainer.style.padding = "25px";
        simulatorContainer.style.boxShadow = "0 6px 16px rgba(0,0,0,0.15)";
        simulatorContainer.style.border = "1px solid #d0d0d0";
        simulatorContainer.style.marginTop = "15px";
        simulatorContainer.style.marginBottom = "40px";

        // --- Create Title ---
        const simulatorTitle = document.createElement("div");
        simulatorTitle.textContent = "Equity Vesting Simulator";
        simulatorTitle.style.fontSize = "22px";
        simulatorTitle.style.fontWeight = "bold";
        simulatorTitle.style.marginBottom = "20px";
        simulatorTitle.style.paddingBottom = "15px";
        simulatorTitle.style.borderBottom = "2px solid #bbdefb";
        simulatorTitle.style.color = "#0d47a1"; // Darker blue for better contrast
        simulatorTitle.style.letterSpacing = "0.3px";
        simulatorTitle.style.textShadow = "0 1px 1px rgba(0,0,0,0.05)";
        simulatorContainer.appendChild(simulatorTitle);

        // --- Create Input Form ---
        const formContainer = document.createElement("div");
        formContainer.style.display = "flex";
        formContainer.style.flexDirection = "column";
        formContainer.style.gap = "20px";
        formContainer.style.marginBottom = "25px";

        // Function to create input group
        function createInputGroup(labelText, inputId, inputType, defaultValue, min, max, step) {
            const group = document.createElement("div");
            group.style.display = "flex";
            group.style.alignItems = "center";
            group.style.gap = "15px";

            const label = document.createElement("label");
            label.htmlFor = inputId;
            label.textContent = labelText;
            label.style.fontWeight = "bold";
            label.style.fontSize = "16px";
            label.style.color = "#222222"; // Darker text for better contrast
            label.style.width = "200px";
            label.style.flexShrink = "0";
            label.style.letterSpacing = "0.2px";

            const input = document.createElement("input");
            input.type = inputType;
            input.id = inputId;
            input.value = defaultValue;
            input.style.padding = "10px 15px";
            input.style.borderRadius = "6px";
            input.style.border = "1px solid #0d47a1"; // Blue border to match theme
            input.style.fontSize = "16px";
            input.style.width = "300px"; // Wider input fields
            input.style.color = "#ffffff"; // White text as requested
            input.style.backgroundColor = "#1976D2"; // Blue background for contrast with white text
            input.style.boxShadow = "inset 0 1px 3px rgba(0,0,0,0.15)";
            input.style.transition = "all 0.2s ease";
            input.style.fontWeight = "500"; // Slightly bolder for better visibility with white text

            // Add hover effect
            input.addEventListener("mouseover", () => {
                input.style.backgroundColor = "#1565C0"; // Slightly darker blue on hover
                input.style.boxShadow = "inset 0 1px 3px rgba(0,0,0,0.2)";
            });

            input.addEventListener("mouseout", () => {
                input.style.backgroundColor = "#1976D2"; // Back to original blue
                input.style.boxShadow = "inset 0 1px 3px rgba(0,0,0,0.15)";
            });

            // Add focus effect
            input.addEventListener("focus", () => {
                input.style.backgroundColor = "#1565C0"; // Slightly darker blue on focus
                input.style.boxShadow = "inset 0 1px 3px rgba(0,0,0,0.2), 0 0 0 2px rgba(25, 118, 210, 0.3)";
                input.style.outline = "none";
            });

            input.addEventListener("blur", () => {
                input.style.backgroundColor = "#1976D2"; // Back to original blue
                input.style.boxShadow = "inset 0 1px 3px rgba(0,0,0,0.15)";
            });

            if (inputType === "number") {
                input.min = min;
                input.max = max;
                input.step = step;
            }

            group.appendChild(label);
            group.appendChild(input);

            return { group, input };
        }

        // Variable to track the currently selected founder
        let selectedFounder = founderNames[0]; // Default to first founder

        // Create founder selection with toggle buttons
        const founderSelectionContainer = document.createElement("div");
        founderSelectionContainer.style.marginBottom = "25px";

        const founderLabel = document.createElement("div");
        founderLabel.textContent = "Select Founder:";
        founderLabel.style.fontWeight = "bold";
        founderLabel.style.fontSize = "16px";
        founderLabel.style.color = "#222222"; // Darker text for better contrast
        founderLabel.style.marginBottom = "15px";
        founderLabel.style.letterSpacing = "0.2px";
        founderSelectionContainer.appendChild(founderLabel);

        // Create toggle buttons container with flex layout
        const toggleButtonsContainer = document.createElement("div");
        toggleButtonsContainer.style.display = "flex";
        toggleButtonsContainer.style.flexWrap = "wrap";
        toggleButtonsContainer.style.gap = "10px";
        toggleButtonsContainer.style.marginBottom = "15px";
        founderSelectionContainer.appendChild(toggleButtonsContainer);

        // Create a button for each founder
        const founderButtons = {};

        founderNames.forEach(name => {
            const button = document.createElement("button");
            button.textContent = name;
            button.style.padding = "10px 15px";
            button.style.borderRadius = "6px";
            button.style.border = "1px solid #0d47a1";
            button.style.fontSize = "16px";
            button.style.fontWeight = "500";
            button.style.cursor = "pointer";
            button.style.transition = "all 0.2s ease";
            button.style.minWidth = "120px";

            // Set initial state (selected for first founder, unselected for others)
            if (name === selectedFounder) {
                button.style.backgroundColor = "#0d47a1";
                button.style.color = "#ffffff";
                button.style.boxShadow = "0 2px 4px rgba(0,0,0,0.2)";
            } else {
                button.style.backgroundColor = "#ffffff";
                button.style.color = "#0d47a1";
                button.style.boxShadow = "0 1px 2px rgba(0,0,0,0.1)";
            }

            // Add hover effect
            button.addEventListener("mouseover", () => {
                if (name !== selectedFounder) {
                    button.style.backgroundColor = "#e3f2fd";
                    button.style.boxShadow = "0 2px 4px rgba(0,0,0,0.15)";
                }
            });

            button.addEventListener("mouseout", () => {
                if (name !== selectedFounder) {
                    button.style.backgroundColor = "#ffffff";
                    button.style.boxShadow = "0 1px 2px rgba(0,0,0,0.1)";
                }
            });

            // Add click handler to select this founder
            button.addEventListener("click", () => {
                // Update the previously selected button
                if (selectedFounder !== name) {
                    const prevButton = founderButtons[selectedFounder];
                    prevButton.style.backgroundColor = "#ffffff";
                    prevButton.style.color = "#0d47a1";
                    prevButton.style.boxShadow = "0 1px 2px rgba(0,0,0,0.1)";

                    // Update the newly selected button
                    button.style.backgroundColor = "#0d47a1";
                    button.style.color = "#ffffff";
                    button.style.boxShadow = "0 2px 4px rgba(0,0,0,0.2)";

                    // Update selected founder
                    selectedFounder = name;

                    // Update visibility of other founders' inputs
                    updateOtherFoundersVisibility();
                }
            });

            // Store reference to button
            founderButtons[name] = button;

            // Add to container
            toggleButtonsContainer.appendChild(button);
        });

        formContainer.appendChild(founderSelectionContainer);

        // Create hours per day input
        const hoursGroup = createInputGroup("Hours per day:", "hours-input", "number", "8", "0", "24", "0.5");
        formContainer.appendChild(hoursGroup.group);
        const hoursInput = hoursGroup.input;

        // Create days input
        const daysGroup = createInputGroup("Number of days:", "days-input", "number", "30", "1", "365", "1");
        formContainer.appendChild(daysGroup.group);
        const daysInput = daysGroup.input;

        // Create a section title for other founders
        const otherFoundersTitle = document.createElement("div");
        otherFoundersTitle.textContent = "Projected Hours for Other Founders";
        otherFoundersTitle.style.fontSize = "17px";
        otherFoundersTitle.style.fontWeight = "bold";
        otherFoundersTitle.style.marginTop = "20px";
        otherFoundersTitle.style.marginBottom = "15px";
        otherFoundersTitle.style.color = "#1976D2"; // Blue color for better visibility
        otherFoundersTitle.style.paddingBottom = "8px";
        otherFoundersTitle.style.borderBottom = "1px solid #e3f2fd";
        formContainer.appendChild(otherFoundersTitle);

        // Create inputs for other founders' hours per day
        const otherFoundersInputs = {};

        founderNames.forEach(name => {
            const otherFounderGroup = createInputGroup(
                `${name}'s hours per day:`,
                `${name.toLowerCase()}-hours-input`,
                "number",
                "2", // Default to 2 hours per day for other founders
                "0",
                "24",
                "0.5"
            );

            // Hide the input for the currently selected founder
            otherFounderGroup.group.style.display = "flex";
            otherFoundersInputs[name] = otherFounderGroup.input;

            formContainer.appendChild(otherFounderGroup.group);
        });

        // Function to update visibility of other founders' inputs
        function updateOtherFoundersVisibility() {
            founderNames.forEach(name => {
                const inputGroup = otherFoundersInputs[name].parentElement;
                if (name === selectedFounder) {
                    inputGroup.style.display = "none";
                } else {
                    inputGroup.style.display = "flex";
                }
            });
        }

        // Initialize visibility of other founders' inputs
        updateOtherFoundersVisibility();

        // Create simulate button
        const buttonContainer = document.createElement("div");
        buttonContainer.style.display = "flex";
        buttonContainer.style.justifyContent = "flex-start";
        buttonContainer.style.marginTop = "10px";

        const simulateButton = document.createElement("button");
        simulateButton.textContent = "Simulate Vesting";
        simulateButton.style.backgroundColor = "#0d47a1"; // Darker blue for better contrast
        simulateButton.style.color = "white";
        simulateButton.style.border = "none";
        simulateButton.style.borderRadius = "6px";
        simulateButton.style.padding = "12px 25px";
        simulateButton.style.fontSize = "16px";
        simulateButton.style.fontWeight = "bold";
        simulateButton.style.cursor = "pointer";
        simulateButton.style.boxShadow = "0 3px 6px rgba(0,0,0,0.2)";
        simulateButton.style.transition = "all 0.2s ease";
        simulateButton.style.letterSpacing = "0.5px";

        simulateButton.addEventListener("mouseover", () => {
            simulateButton.style.backgroundColor = "#083378"; // Even darker blue on hover
            simulateButton.style.transform = "translateY(-2px)";
            simulateButton.style.boxShadow = "0 5px 10px rgba(0,0,0,0.25)";
        });

        simulateButton.addEventListener("mouseout", () => {
            simulateButton.style.backgroundColor = "#0d47a1"; // Match the original color
            simulateButton.style.transform = "translateY(0)";
            simulateButton.style.boxShadow = "0 3px 6px rgba(0,0,0,0.2)";
        });

        buttonContainer.appendChild(simulateButton);
        formContainer.appendChild(buttonContainer);

        simulatorContainer.appendChild(formContainer);

        // --- Create Results Container ---
        const resultsContainer = document.createElement("div");
        resultsContainer.style.display = "none"; // Initially hidden
        resultsContainer.style.marginTop = "30px";

        // Create results title
        const resultsTitle = document.createElement("div");
        resultsTitle.textContent = "Simulation Results";
        resultsTitle.style.fontSize = "20px";
        resultsTitle.style.fontWeight = "bold";
        resultsTitle.style.marginBottom = "18px";
        resultsTitle.style.paddingBottom = "12px";
        resultsTitle.style.borderBottom = "2px solid #bbdefb";
        resultsTitle.style.color = "#0d47a1"; // Darker blue for better contrast
        resultsTitle.style.letterSpacing = "0.3px";
        resultsTitle.style.textShadow = "0 1px 1px rgba(0,0,0,0.05)";
        resultsContainer.appendChild(resultsTitle);

        // Create results content
        const resultsContent = document.createElement("div");
        resultsContent.style.display = "flex";
        resultsContent.style.flexDirection = "column";
        resultsContent.style.gap = "25px";
        resultsContainer.appendChild(resultsContent);

        // Create before/after blocks (stacked vertically)
        const beforeColumn = document.createElement("div");
        beforeColumn.style.width = "100%";
        beforeColumn.style.backgroundColor = "#f5f9ff"; // Light blue background
        beforeColumn.style.borderRadius = "8px";
        beforeColumn.style.padding = "22px";
        beforeColumn.style.border = "1px solid #d4e5f9"; // Light blue border
        beforeColumn.style.boxShadow = "0 3px 6px rgba(0,0,0,0.08)";

        const beforeTitle = document.createElement("div");
        beforeTitle.textContent = "Current Equity";
        beforeTitle.style.fontSize = "18px";
        beforeTitle.style.fontWeight = "bold";
        beforeTitle.style.marginBottom = "16px";
        beforeTitle.style.paddingBottom = "10px";
        beforeTitle.style.borderBottom = "1px solid #bbdefb"; // Light blue border
        beforeTitle.style.color = "#0d47a1"; // Darker blue for better contrast
        beforeTitle.style.letterSpacing = "0.2px";
        beforeColumn.appendChild(beforeTitle);

        const afterColumn = document.createElement("div");
        afterColumn.style.width = "100%";
        afterColumn.style.backgroundColor = "#f5fff7"; // Light green background
        afterColumn.style.borderRadius = "8px";
        afterColumn.style.padding = "22px";
        afterColumn.style.border = "1px solid #d4f9db"; // Light green border
        afterColumn.style.boxShadow = "0 3px 6px rgba(0,0,0,0.08)";

        const afterTitle = document.createElement("div");
        afterTitle.textContent = "Projected Equity";
        afterTitle.style.fontSize = "18px";
        afterTitle.style.fontWeight = "bold";
        afterTitle.style.marginBottom = "16px";
        afterTitle.style.paddingBottom = "10px";
        afterTitle.style.borderBottom = "1px solid #c8e6c9"; // Light green border
        afterTitle.style.color = "#2e7d32"; // Darker green for better contrast
        afterTitle.style.letterSpacing = "0.2px";
        afterColumn.appendChild(afterTitle);

        resultsContent.appendChild(beforeColumn);
        resultsContent.appendChild(afterColumn);

        // Create tables for before/after
        const beforeTable = document.createElement("table");
        beforeTable.style.width = "100%";
        beforeTable.style.borderCollapse = "separate";
        beforeTable.style.borderSpacing = "0 8px";
        beforeColumn.appendChild(beforeTable);

        const afterTable = document.createElement("table");
        afterTable.style.width = "100%";
        afterTable.style.borderCollapse = "separate";
        afterTable.style.borderSpacing = "0 8px";
        afterColumn.appendChild(afterTable);

        // Function to create table headers
        function createTableHeader(table) {
            const thead = document.createElement("thead");
            const headerRow = document.createElement("tr");

            const headers = ["Founder", "Hours", "Equity %"];
            const widths = ["40%", "30%", "30%"];

            headers.forEach((headerText, index) => {
                const th = document.createElement("th");
                th.textContent = headerText;
                th.style.padding = "12px 15px";
                th.style.textAlign = index > 0 ? "right" : "left";
                th.style.color = "#0d47a1"; // Darker blue for better contrast
                th.style.fontWeight = "bold";
                th.style.fontSize = "16px";
                th.style.letterSpacing = "0.2px";
                th.style.borderBottom = "2px solid #e3f2fd";
                th.style.width = widths[index];
                headerRow.appendChild(th);
            });

            thead.appendChild(headerRow);
            table.appendChild(thead);

            return document.createElement("tbody");
        }

        // Create table bodies
        const beforeTbody = createTableHeader(beforeTable);
        beforeTable.appendChild(beforeTbody);

        const afterTbody = createTableHeader(afterTable);
        afterTable.appendChild(afterTbody);

        // Function to get color for founder
        function getFounderColor(name, alpha = 1) {
            const index = founderNames.indexOf(name);

            const colors = [
                `rgba(25, 118, 210, ${alpha})`,   // Darker Blue
                `rgba(198, 40, 40, ${alpha})`,    // Darker Red
                `rgba(46, 125, 50, ${alpha})`,    // Darker Green
                `rgba(239, 108, 0, ${alpha})`,    // Darker Orange
                `rgba(123, 31, 162, ${alpha})`,   // Darker Purple
                `rgba(0, 131, 143, ${alpha})`,    // Darker Teal
                `rgba(191, 54, 12, ${alpha})`,    // Darker Deep Orange
                `rgba(93, 64, 55, ${alpha})`,     // Darker Brown
                `rgba(48, 63, 159, ${alpha})`,    // Darker Indigo
                `rgba(158, 157, 36, ${alpha})`    // Darker Lime
            ];

            return index >= 0 ? colors[index % colors.length] : `rgba(100, 100, 100, ${alpha})`;
        }

        // Function to create a table row
        function createTableRow(tbody, founderName, hours, percentage, highlight = false) {
            const row = document.createElement("tr");
            if (highlight) {
                row.style.backgroundColor = "rgba(25, 118, 210, 0.05)";
            }

            // Founder name cell
            const nameCell = document.createElement("td");
            nameCell.style.padding = "12px 15px";
            nameCell.style.fontWeight = "bold";
            nameCell.style.fontSize = "16px";
            nameCell.style.color = getFounderColor(founderName, 1).replace("rgba", "rgb").replace(", 1)", ")");
            nameCell.textContent = founderName;

            // Hours cell
            const hoursCell = document.createElement("td");
            hoursCell.style.padding = "12px 15px";
            hoursCell.style.textAlign = "right";

            const hoursText = document.createElement("span");
            hoursText.textContent = hours.toFixed(2);
            hoursText.style.color = getFounderColor(founderName, 1).replace("rgba", "rgb").replace(", 1)", ")");
            hoursText.style.fontWeight = "bold";
            hoursText.style.fontSize = "16px";
            hoursText.style.display = "inline-block";
            hoursText.style.minWidth = "60px";
            hoursText.style.textAlign = "right";

            hoursCell.appendChild(hoursText);

            // Percentage cell
            const percentageCell = document.createElement("td");
            percentageCell.style.padding = "12px 15px";
            percentageCell.style.textAlign = "right";

            const percentageText = document.createElement("span");
            percentageText.textContent = percentage.toFixed(2) + "%";
            percentageText.style.color = getFounderColor(founderName, 1).replace("rgba", "rgb").replace(", 1)", ")");
            percentageText.style.fontWeight = "bold";
            percentageText.style.fontSize = "16px";
            percentageText.style.display = "inline-block";
            percentageText.style.minWidth = "70px";
            percentageText.style.textAlign = "right";

            percentageCell.appendChild(percentageText);

            // Add cells to row
            row.appendChild(nameCell);
            row.appendChild(hoursCell);
            row.appendChild(percentageCell);

            // Add row to table body
            tbody.appendChild(row);
        }

        // Function to populate the "before" table
        function populateBeforeTable() {
            // Clear existing rows
            beforeTbody.innerHTML = "";

            // Add a row for each founder
            founderNames.forEach(name => {
                if (baseValues.hasOwnProperty(name)) {
                    createTableRow(
                        beforeTbody,
                        name,
                        rawHours[name],
                        currentPercentages[name],
                        name === selectedFounder
                    );
                }
            });

            // Calculate total hours
            const totalHours = Object.values(rawHours).reduce((sum, hours) =>
                (typeof hours === 'number' && !isNaN(hours) ? sum + hours : sum), 0);

            // Add total row
            const totalRow = document.createElement("tr");
            totalRow.style.borderTop = "1px solid #e0e0e0";
            totalRow.style.backgroundColor = "#000000"; // Black background for total row
            totalRow.style.color = "#ffffff"; // White text for better contrast on black

            const totalLabelCell = document.createElement("td");
            totalLabelCell.style.padding = "12px 15px";
            totalLabelCell.style.fontWeight = "bold";
            totalLabelCell.style.fontSize = "16px";
            totalLabelCell.textContent = "Total";

            const totalHoursCell = document.createElement("td");
            totalHoursCell.style.padding = "12px 15px";
            totalHoursCell.style.textAlign = "right";
            totalHoursCell.style.fontWeight = "bold";
            totalHoursCell.textContent = totalHours.toFixed(2);

            const totalPercentCell = document.createElement("td");
            totalPercentCell.style.padding = "12px 15px";
            totalPercentCell.style.textAlign = "right";
            totalPercentCell.style.fontWeight = "bold";
            totalPercentCell.textContent = "100.00%";

            totalRow.appendChild(totalLabelCell);
            totalRow.appendChild(totalHoursCell);
            totalRow.appendChild(totalPercentCell);

            beforeTbody.appendChild(totalRow);
        }

        // Function to populate the "after" table based on simulation
        function populateAfterTable(selectedFounder, additionalHours, otherFoundersHours) {
            // Clear existing rows
            afterTbody.innerHTML = "";

            // Calculate new hours and scores
            const newHours = {...rawHours};
            const newScores = {...scores};

            // Add the selected founder's hours and calculate weighted points
            if (baseValues.hasOwnProperty(selectedFounder)) {
                const baseValue = baseValues[selectedFounder];
                const additionalPoints = baseValue * additionalHours;

                newHours[selectedFounder] += additionalHours;
                newScores[selectedFounder] += additionalPoints;
            }

            // Add other founders' hours and calculate weighted points
            const numDays = parseInt(daysInput.value);
            founderNames.forEach(name => {
                if (name !== selectedFounder && otherFoundersHours[name] > 0 && baseValues.hasOwnProperty(name)) {
                    const additionalFounderHours = otherFoundersHours[name] * numDays;
                    const baseValue = baseValues[name];
                    const additionalPoints = baseValue * additionalFounderHours;

                    newHours[name] += additionalFounderHours;
                    newScores[name] += additionalPoints;
                }
            });

            // Calculate total new weighted points
            const totalNewScore = Object.values(newScores).reduce((sum, score) =>
                (typeof score === 'number' && !isNaN(score) ? sum + score : sum), 0);

            // Calculate new percentages based on weighted points
            const newPercentages = {};
            founderNames.forEach(name => {
                if (baseValues.hasOwnProperty(name)) {
                    newPercentages[name] = totalNewScore > 0 ?
                        (newScores[name] / totalNewScore) * 100 : 0;
                } else {
                    newPercentages[name] = 0;
                }
            });

            // Add a row for each founder
            founderNames.forEach(name => {
                if (baseValues.hasOwnProperty(name)) {
                    createTableRow(
                        afterTbody,
                        name,
                        newHours[name],
                        newPercentages[name],
                        name === selectedFounder
                    );
                }
            });

            // Calculate total hours
            const totalNewHours = Object.values(newHours).reduce((sum, hours) =>
                (typeof hours === 'number' && !isNaN(hours) ? sum + hours : sum), 0);

            // Add total row
            const totalRow = document.createElement("tr");
            totalRow.style.borderTop = "1px solid #e0e0e0";
            totalRow.style.backgroundColor = "#000000"; // Black background for total row
            totalRow.style.color = "#ffffff"; // White text for better contrast on black

            const totalLabelCell = document.createElement("td");
            totalLabelCell.style.padding = "12px 15px";
            totalLabelCell.style.fontWeight = "bold";
            totalLabelCell.style.fontSize = "16px";
            totalLabelCell.textContent = "Total";

            const totalHoursCell = document.createElement("td");
            totalHoursCell.style.padding = "12px 15px";
            totalHoursCell.style.textAlign = "right";
            totalHoursCell.style.fontWeight = "bold";
            totalHoursCell.textContent = totalNewHours.toFixed(2);

            const totalPercentCell = document.createElement("td");
            totalPercentCell.style.padding = "12px 15px";
            totalPercentCell.style.textAlign = "right";
            totalPercentCell.style.fontWeight = "bold";
            totalPercentCell.textContent = "100.00%";

            totalRow.appendChild(totalLabelCell);
            totalRow.appendChild(totalHoursCell);
            totalRow.appendChild(totalPercentCell);

            afterTbody.appendChild(totalRow);

            return {
                newHours,
                newScores,
                newPercentages,
                totalNewScore,
                totalNewHours,
                otherFoundersHours
            };
        }

        // Create summary section
        const summaryContainer = document.createElement("div");
        summaryContainer.style.marginTop = "30px";
        summaryContainer.style.backgroundColor = "#e8f5ff"; // Slightly darker blue background
        summaryContainer.style.borderRadius = "10px";
        summaryContainer.style.padding = "25px";
        summaryContainer.style.border = "1px solid #bbdefb";
        summaryContainer.style.boxShadow = "0 4px 8px rgba(0,0,0,0.08)";
        resultsContainer.appendChild(summaryContainer);

        const summaryTitle = document.createElement("div");
        summaryTitle.textContent = "Summary";
        summaryTitle.style.fontSize = "19px";
        summaryTitle.style.fontWeight = "bold";
        summaryTitle.style.marginBottom = "18px";
        summaryTitle.style.paddingBottom = "10px";
        summaryTitle.style.borderBottom = "1px solid #bbdefb";
        summaryTitle.style.color = "#0d47a1"; // Darker blue for better contrast
        summaryTitle.style.letterSpacing = "0.3px";
        summaryContainer.appendChild(summaryTitle);

        const summaryContent = document.createElement("div");
        summaryContent.style.fontSize = "16px";
        summaryContent.style.lineHeight = "1.7";
        summaryContent.style.color = "#222222"; // Darker text for better contrast
        summaryContainer.appendChild(summaryContent);

        // Add results container to main container
        simulatorContainer.appendChild(resultsContainer);

        // --- Add Event Listener for Simulate Button ---
        simulateButton.addEventListener("click", () => {
            // selectedFounder is now a global variable set by the toggle buttons
            const hoursPerDay = parseFloat(hoursInput.value);
            const numDays = parseInt(daysInput.value);

            if (isNaN(hoursPerDay) || isNaN(numDays) || hoursPerDay < 0 || numDays < 1) {
                alert("Please enter valid values for hours per day and number of days.");
                return;
            }

            // Get hours for other founders
            const otherFoundersHours = {};
            let anyOtherFounderContributing = false;

            founderNames.forEach(name => {
                if (name !== selectedFounder) {
                    const hoursInput = otherFoundersInputs[name];
                    const hours = parseFloat(hoursInput.value);
                    otherFoundersHours[name] = isNaN(hours) ? 0 : hours;

                    if (otherFoundersHours[name] > 0) {
                        anyOtherFounderContributing = true;
                    }
                } else {
                    otherFoundersHours[name] = 0; // Selected founder's hours are handled separately
                }
            });

            const additionalHours = hoursPerDay * numDays;

            // Show results container
            resultsContainer.style.display = "block";

            // Populate tables
            populateBeforeTable();
            const { newPercentages, newScores } = populateAfterTable(selectedFounder, additionalHours, otherFoundersHours);

            // Calculate weighted points
            let baseValue = baseValues[selectedFounder] || 0;
            let additionalPoints = baseValue * additionalHours;

            // Calculate equity change
            const equityBefore = currentPercentages[selectedFounder];
            const equityAfter = newPercentages[selectedFounder];
            const equityChange = equityAfter - equityBefore;

            // Update summary
            summaryContent.innerHTML = "";

            // Add base value info
            const baseValueInfo = document.createElement("p");
            baseValueInfo.style.margin = "0 0 20px 0";
            baseValueInfo.style.fontSize = "16px";
            baseValueInfo.style.color = "#333333"; // Darker text for better contrast
            baseValueInfo.style.padding = "12px 15px";
            baseValueInfo.style.backgroundColor = "rgba(187, 222, 251, 0.2)"; // Very light blue background
            baseValueInfo.style.borderRadius = "6px";
            baseValueInfo.innerHTML = `<strong style="color: ${getFounderColor(selectedFounder, 1).replace("rgba", "rgb").replace(", 1)", ")")}">${selectedFounder}</strong>'s base value multiplier is <strong>${baseValue}×</strong>, which means each hour contributed generates <strong>${baseValue}</strong> weighted points.`;
            summaryContent.appendChild(baseValueInfo);

            const summaryText = document.createElement("p");
            summaryText.style.margin = "0 0 10px 0";

            if (equityChange > 0) {
                summaryText.innerHTML = `If <strong style="color: ${getFounderColor(selectedFounder, 1).replace("rgba", "rgb").replace(", 1)", ")")}">${selectedFounder}</strong> contributes <strong>${hoursPerDay}</strong> hours per day for <strong>${numDays}</strong> days (total: <strong>${additionalHours}</strong> hours), their equity will <strong style="color: #2e7d32">increase by ${equityChange.toFixed(2)}%</strong> (from ${equityBefore.toFixed(2)}% to ${equityAfter.toFixed(2)}%).`;
            } else if (equityChange < 0) {
                summaryText.innerHTML = `If <strong style="color: ${getFounderColor(selectedFounder, 1).replace("rgba", "rgb").replace(", 1)", ")")}">${selectedFounder}</strong> contributes <strong>${hoursPerDay}</strong> hours per day for <strong>${numDays}</strong> days (total: <strong>${additionalHours}</strong> hours), their equity will <strong style="color: #c62828">decrease by ${Math.abs(equityChange).toFixed(2)}%</strong> (from ${equityBefore.toFixed(2)}% to ${equityAfter.toFixed(2)}%).`;
            } else {
                summaryText.innerHTML = `If <strong style="color: ${getFounderColor(selectedFounder, 1).replace("rgba", "rgb").replace(", 1)", ")")}">${selectedFounder}</strong> contributes <strong>${hoursPerDay}</strong> hours per day for <strong>${numDays}</strong> days (total: <strong>${additionalHours}</strong> hours), their equity will <strong>remain unchanged</strong> at ${equityBefore.toFixed(2)}%.`;
            }

            summaryContent.appendChild(summaryText);

            // Add note about other founders' contributions
            if (anyOtherFounderContributing) {
                const otherFoundersNote = document.createElement("p");
                otherFoundersNote.style.margin = "15px 0";
                otherFoundersNote.style.fontSize = "15px";
                otherFoundersNote.style.color = "#333333";
                otherFoundersNote.style.padding = "12px 15px";
                otherFoundersNote.style.backgroundColor = "rgba(200, 230, 201, 0.2)"; // Very light green background
                otherFoundersNote.style.borderRadius = "6px";
                otherFoundersNote.style.lineHeight = "1.5";

                let otherFoundersText = "This simulation includes projected contributions from: ";
                const contributingFounders = founderNames.filter(name =>
                    name !== selectedFounder && otherFoundersHours[name] > 0
                );

                contributingFounders.forEach((name, index) => {
                    const hours = otherFoundersHours[name];
                    const totalHours = hours * numDays;

                    otherFoundersText += `<strong style="color: ${getFounderColor(name, 1).replace("rgba", "rgb").replace(", 1)", ")")}">${
                        name}</strong> (${hours} hrs/day, total: ${totalHours} hrs)`;

                    if (index < contributingFounders.length - 1) {
                        otherFoundersText += ", ";
                    }
                });

                otherFoundersNote.innerHTML = otherFoundersText + ".";
                summaryContent.appendChild(otherFoundersNote);
            } else {
                // Add note about no other founders contributing
                const noteText = document.createElement("p");
                noteText.style.margin = "15px 0 0 0";
                noteText.style.fontSize = "15px";
                noteText.style.color = "#555555";
                noteText.style.fontStyle = "italic";
                noteText.style.padding = "12px 15px";
                noteText.style.backgroundColor = "rgba(0, 0, 0, 0.03)"; // Very light gray background
                noteText.style.borderRadius = "6px";
                noteText.textContent = "Note: This simulation assumes no additional contributions from other founders during this period.";
                summaryContent.appendChild(noteText);
            }

            // Scroll to results
            resultsContainer.scrollIntoView({ behavior: 'smooth' });
        });

    } catch (error) {
        simulatorLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```

## How to Use This Simulator

1. **Select a Founder**: Choose which founder's equity you want to simulate.
2. **Enter Hours per Day**: Input how many hours per day the founder will contribute.
3. **Enter Number of Days**: Specify how many days this contribution will continue.
4. **Click "Simulate Vesting"**: The tool will calculate the projected equity changes.

The simulation shows:
- Current equity distribution
- Projected equity after the specified contribution period
- Summary of equity changes (gain or loss)

**Note**: This simulation allows you to include projected contributions from other founders during the specified period, providing a more realistic view of how equity percentages will change over time.
