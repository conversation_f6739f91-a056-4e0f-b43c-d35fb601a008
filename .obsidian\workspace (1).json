{"main": {"id": "068c9a57d79c0dc5", "type": "split", "children": [{"id": "5468f42770ca16ba", "type": "tabs", "children": [{"id": "eb79bcfd96c0a1da", "type": "leaf", "state": {"type": "markdown", "state": {"file": "00. Work/Work Log.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "Work Log"}}]}], "direction": "vertical"}, "left": {"id": "12e40291654f2e34", "type": "split", "children": [{"id": "919a6e82aa9de3b4", "type": "tabs", "children": [{"id": "92b0a03073cd3d51", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "afcc5b318999abca", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "5f0469faaa33ab25", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "0aedcfa8f24d77ca", "type": "split", "children": [{"id": "82c189b35e2687a6", "type": "tabs", "children": [{"id": "b9d369db59c2529f", "type": "leaf", "state": {"type": "backlink", "state": {"file": "00. Work/Equity.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Equity"}}, {"id": "8e67a6a4d0bd9675", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "00. Work/Equity.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Equity"}}, {"id": "3f9fb7b082f306b7", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "934be630ceac7723", "type": "leaf", "state": {"type": "outline", "state": {"file": "00. Work/Equity.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Equity"}}], "currentTab": 3}], "direction": "horizontal", "width": 200, "collapsed": true}, "left-ribbon": {"hiddenItems": {"templater-obsidian:Templater": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "obsidian-kanban:Create new board": false, "hidden-folder-obsidian:Show Folders": false}}, "active": "eb79bcfd96c0a1da", "lastOpenFiles": ["00. Work/Submit Work.md", "99 - data/equitySplit/Founders/contribution 127.md", "00. Work/Work Log.md", "99 - data/equitySplit/Founders/contribution 124.md", "00. Work/Equity.md", "99 - data/equitySplit/Equity Reference.md", "99 - data/equitySplit/Founders/contribution 122.md", "99 - data/equitySplit/Founders/contribution 118.md", "99 - data/equitySplit/Founders/contribution 116.md", "99 - data/equitySplit/Founders/contribution 114.md", "99 - data/equitySplit/Founders/contribution 113.md", "99 - data/equitySplit/Founders/contribution 105.md", "desktop.ini", "99 - data/equitySplit/Founders/contribution 106.md", "06. 3D Hero Assets/01. Ghost AI.md", "Pasted image 20250514114035.png", "00. Work/Leaderboard.md", "00. Work/Vesting Simulator.md", "00. Work/Work Board.md", "99 - data/equitySplit/Founders/contribution 99.md", "01. GDD/02. Story.md", "01. GDD/01. Introduction.md", "99 - data/equitySplit/Founders/contribution 97.md", "01. GDD/04. Characters.md", "<PERSON><PERSON><PERSON>'s Work/All Kitchen Utensils.md", "<PERSON><PERSON><PERSON>'s Work/Lockpick implementation with the door opening.md", "01. GDD/07. Development Timeline.md", "Untitled Kanban.md", "2025-04-29 19-56-46.mp4", "Media/2025-04-22 14-31-47.mp4", "Media/2025-04-22 14-26-27.mp4", "Media/LevelClear.png", "Media/Pasted image 20241225161719.png", "Media/Screenshot 2024-09-12 161721.png", "Media/Screenshot 2024-09-12 161433.png", "Media", "00. Work/Reference Files", "Untitled", "Images/Pasted image 20241225161719.png", "Images/LevelClear.png", "06. 3D Hero Assets", "05. Programming", "Socials", "Images/Pasted image 20241225164715.png", "Images/Screenshot 2024-09-12 161433.png", "Images/Screenshot 2024-09-12 161721.png", "Untitled.canvas"]}